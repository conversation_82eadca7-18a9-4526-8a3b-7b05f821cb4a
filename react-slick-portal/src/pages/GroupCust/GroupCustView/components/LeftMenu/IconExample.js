import React from 'react';
import { Card, Row, Col, Space, Typography } from 'antd';
import { 
  FrameIcon, 
  Frame1Icon, 
  Frame2Icon, 
  Frame3Icon,
  DynamicIcon,
  ICON_COLORS 
} from './menuIcons';

const { Title, Paragraph } = Typography;

/**
 * 菜单图标使用示例
 * 展示如何使用动态颜色和大小
 */
const IconExample = () => {
  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>菜单图标使用示例</Title>
      
      {/* 基础使用 */}
      <Card title="1. 基础使用" style={{ marginBottom: 16 }}>
        <Space size="large">
          <FrameIcon />
          <Frame1Icon />
          <Frame2Icon />
          <Frame3Icon />
        </Space>
      </Card>

      {/* 动态颜色 */}
      <Card title="2. 动态颜色" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <FrameIcon color={ICON_COLORS.PRIMARY} size={24} />
              <div style={{ marginTop: 8 }}>主色</div>
            </div>
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <FrameIcon color={ICON_COLORS.SUCCESS} size={24} />
              <div style={{ marginTop: 8 }}>成功</div>
            </div>
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <FrameIcon color={ICON_COLORS.WARNING} size={24} />
              <div style={{ marginTop: 8 }}>警告</div>
            </div>
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <FrameIcon color={ICON_COLORS.ERROR} size={24} />
              <div style={{ marginTop: 8 }}>错误</div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 不同尺寸 */}
      <Card title="3. 不同尺寸" style={{ marginBottom: 16 }}>
        <Space size="large" align="center">
          <FrameIcon size={12} />
          <FrameIcon size={16} />
          <FrameIcon size={20} />
          <FrameIcon size={24} />
          <FrameIcon size={32} />
        </Space>
        <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
          12px / 16px / 20px / 24px / 32px
        </div>
      </Card>

      {/* 动态使用 */}
      <Card title="4. 动态使用" style={{ marginBottom: 16 }}>
        <Space size="large">
          <DynamicIcon name="frame" color="#ff6b6b" size={20} />
          <DynamicIcon name="frame1" color="#4ecdc4" size={20} />
          <DynamicIcon name="frame2" color="#45b7d1" size={20} />
          <DynamicIcon name="frame3" color="#96ceb4" size={20} />
        </Space>
      </Card>

      {/* 使用代码示例 */}
      <Card title="5. 使用代码示例">
        <Paragraph>
          <Title level={4}>基础使用</Title>
          <pre style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
{`import { FrameIcon, ICON_COLORS } from './menuIcons';

// 基础使用
<FrameIcon />

// 自定义颜色和大小
<FrameIcon color={ICON_COLORS.PRIMARY} size={24} />

// 自定义颜色
<FrameIcon color="#ff6b6b" size={20} />`}
          </pre>
        </Paragraph>

        <Paragraph>
          <Title level={4}>动态使用</Title>
          <pre style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
{`import { DynamicIcon, ICON_COLORS } from './menuIcons';

// 动态图标
<DynamicIcon name="frame1" color={ICON_COLORS.SUCCESS} size={20} />`}
          </pre>
        </Paragraph>

        <Paragraph>
          <Title level={4}>在菜单中使用</Title>
          <pre style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
{`import { OpenSvgIcon } from './menuIcons';

<Menu.SubMenu
  title={(
    <span>
      <OpenSvgIcon size={16} color="#1890ff" style={{ marginRight: 8 }} />
      <span>菜单项</span>
    </span>
  )}
>`}
          </pre>
        </Paragraph>
      </Card>
    </div>
  );
};

export default IconExample;
