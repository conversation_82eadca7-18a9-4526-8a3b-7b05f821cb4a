import React from 'react';
import { Card, Space, Typography } from 'antd';
import { 
  FrameIcon, 
  Frame1Icon, 
  Frame2Icon, 
  Frame3Icon,
  ICON_COLORS 
} from './menuIcons';

const { Title, Paragraph } = Typography;

/**
 * 菜单图标使用示例
 * 展示如何使用动态颜色和大小
 */
const IconExample = () => {
  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>菜单图标使用示例</Title>
      
      {/* 基础使用 */}
      <Card title="1. 基础使用" style={{ marginBottom: 16 }}>
        <Space size="large">
          <FrameIcon />
          <Frame1Icon />
          <Frame2Icon />
          <Frame3Icon />
        </Space>
      </Card>

      {/* 动态颜色 */}
      <Card title="2. 动态颜色" style={{ marginBottom: 16 }}>
        <Space size="large">
          <FrameIcon style={{ fontSize: 24, color: ICON_COLORS.PRIMARY }} />
          <FrameIcon style={{ fontSize: 24, color: ICON_COLORS.SUCCESS }} />
          <FrameIcon style={{ fontSize: 24, color: ICON_COLORS.WARNING }} />
          <FrameIcon style={{ fontSize: 24, color: ICON_COLORS.ERROR }} />
        </Space>
      </Card>

      {/* 不同尺寸 */}
      <Card title="3. 不同尺寸" style={{ marginBottom: 16 }}>
        <Space size="large" align="center">
          <FrameIcon style={{ fontSize: 12 }} />
          <FrameIcon style={{ fontSize: 16 }} />
          <FrameIcon style={{ fontSize: 20 }} />
          <FrameIcon style={{ fontSize: 24 }} />
          <FrameIcon style={{ fontSize: 32 }} />
        </Space>
        <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
          12px / 16px / 20px / 24px / 32px
        </div>
      </Card>

      {/* 使用代码示例 */}
      <Card title="4. 使用代码示例">
        <Paragraph>
          <Title level={4}>基础使用</Title>
          <pre style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
{`import { FrameIcon, ICON_COLORS } from './menuIcons';

// 基础使用
<FrameIcon />

// 自定义颜色和大小
<FrameIcon style={{ fontSize: 24, color: ICON_COLORS.PRIMARY }} />

// 自定义颜色
<FrameIcon style={{ fontSize: 20, color: '#ff6b6b' }} />`}
          </pre>
        </Paragraph>

        <Paragraph>
          <Title level={4}>在菜单中使用</Title>
          <pre style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
{`import { OpenSvgIcon } from './menuIcons';

<Menu.SubMenu
  title={(
    <span>
      <OpenSvgIcon style={{ fontSize: 16, color: '#1890ff', marginRight: 8 }} />
      <span>菜单项</span>
    </span>
  )}
>`}
          </pre>
        </Paragraph>
      </Card>
    </div>
  );
};

export default IconExample;
