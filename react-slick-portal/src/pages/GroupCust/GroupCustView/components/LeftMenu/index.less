.groupInfoContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  .searchWrapper {
    padding: 16px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .companyInfo {
    padding: 16px 0;
    display: flex;
    align-items: start;

    .companyLogo {
      width: 56px;
      height: 56px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    .companyName {
      font-size: 16px;
      height: 24px;
      line-height: 24px;
      color: #333333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 192px;
      font-weight: 500;
      margin-bottom: 12px;
    }

    .tags {
      .tag {
        display: inline-block;
        background-color: #f0f9eb;
        color: #67c23a;
        font-size: 12px;
        padding: 1px 4px;
        border-radius: 2px;
        margin: 0 4px 8px 0;
        white-space: nowrap;

        &.aClass {
          background-color: #f0f9eb;
          color: #67c23a;
        }

        &.industry {
          background-color: #fffbe6;
          color: #e6a23c;
        }

        &.manager {
          background-color: #fde2e2;
          color: #f56c6c;
        }
      }
    }
  }

  .infoRow {
    display: flex;
    align-items: baseline;
    font-size: 14px;

    span {
      font-weight: 400;
      color: #999999;
      margin-top: 8px;
    }

    span:last-child {
      flex: 1;
      margin-bottom: 0;
      padding-left: 5px;
      white-space: pre-line;
      color: #333333;
    }

    &:last-child {
      margin-bottom: 20px;
    }
  }

  .antMenu {
    flex: 1;
    overflow-y: auto;
    background: transparent;
    border: none;
    // padding: 12px;


    :global {

      // Common styles for all first level items
      .ant-menu-submenu > .ant-menu-submenu-title,
      .ant-menu-item {
        height: 40px;
        background: #FFFFFF;
        margin: 0;
        padding: 9px 14px;
        display: flex;
        // align-items: center;
        color: #333333;

        span {
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          text-align: left;
        }

        &:hover {
          background: #E6F4FB;
          color: #0085D0;
        }
      }

      // Second level items
      .ant-menu-sub {
        padding: 0 0 8px 0;
        background: transparent;
      }

      .ant-menu-submenu .ant-menu-item {
        height: 40px;
        padding: 9px 14px 9px 38px;
        border-radius: 4px;
        color: #666666;

        &::before {
          content: "";
          position: absolute;
          left: -12px;
          top: 50%;
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background: #D9D9D9;
          transform: translateY(-50%);
        }

        span {
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          text-align: left;
        }


        &:hover {
          background: #E6F4FB;
          color: #0085D0;

          &::before {
            background: #0085D0;
          }

          span {
            color: #0085D0;
          }
        }
      }

      // Selected state for ALL menu items
      .ant-menu-item-selected,
      .ant-menu-submenu-selected > .ant-menu-submenu-title {
        background: linear-gradient(360deg, #0085D0 0%, #2DACF3 100%);
        box-shadow: 2px 0px 4px 0px rgba(0, 0, 0, 0.08);
        color: #ffffff !important;

        span {
          color: #ffffff !important;
          font-weight: 500;
        }

        &:hover {
          background: linear-gradient(360deg, #0085D0 0%, #2DACF3 100%) !important;
        }
      }

      // Special handling for submenu items when selected
      .ant-menu-submenu .ant-menu-item-selected {
        &::before {
          background: #ffffff;
          width: 6px;
          height: 6px;
        }
      }

      // Arrow in submenu
      .ant-menu-submenu-arrow {
        color: #999999;
      }

      .ant-menu-submenu-selected .ant-menu-submenu-arrow {
        color: #ffffff !important;
      }

      // Remove default border
      .ant-menu-item::after,
      .ant-menu-submenu::after {
        display: none;
      }
    }
  }
}

// 搜索相关样式
.searchInput {
  margin-bottom: 16px;

  :global {
    .ant-input {
      border-radius: 4px;
      height: 40px;
      font-size: 14px;
    }

    .ant-input-search-button {
      height: 40px;
      border-radius: 0 4px 4px 0;
    }
  }
}

.searchResultList {
  max-height: 300px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f5f5;
  }
}

.searchResultItem {
  padding: 10px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #E6F4FB;
    color: #0085D0;
  }
}
