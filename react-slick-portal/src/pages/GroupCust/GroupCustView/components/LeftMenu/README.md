# 菜单图标组件

支持动态颜色和大小的SVG图标组件库，专为LeftMenu组件设计。

## 特性

- 🎨 **动态颜色**：支持通过props动态修改图标颜色
- 📏 **灵活尺寸**：支持数字或字符串形式的尺寸设置
- 🔧 **统一接口**：所有图标组件具有一致的API
- 📦 **按需导入**：支持按需导入，减少打包体积
- 🎯 **向后兼容**：保持与原有代码的兼容性

## 可用图标

- `FrameIcon` - Frame.svg
- `Frame1Icon` - Frame(1).svg
- `Frame2Icon` - Frame(2).svg
- `Frame3Icon` - Frame(3).svg
- `Frame4Icon` - Frame(4).svg
- `Frame5Icon` - Frame(5).svg
- `Frame6Icon` - Frame(6).svg
- `Frame7Icon` - Frame(7).svg
- `Frame8Icon` - Frame(8).svg
- `Frame9Icon` - Frame(9).svg
- `Frame10Icon` - Frame(10).svg
- `Frame11Icon` - Frame(11).svg
- `Frame12Icon` - Frame(12).svg
- `Frame13Icon` - Frame(13).svg
- `Frame14Icon` - Frame(14).svg
- `Frame15Icon` - Frame(15).svg
- `Frame16Icon` - Frame(16).svg
- `Frame17Icon` - Frame(17).svg

## 基础用法

### 1. 直接使用图标组件

```jsx
import { FrameIcon, Frame1Icon } from './menuIcons';

function MyComponent() {
  return (
    <div>
      <FrameIcon size={24} color="#1890ff" />
      <Frame1Icon size={20} color="#52c41a" />
    </div>
  );
}
```

### 2. 使用预设颜色常量

```jsx
import { FrameIcon, ICON_COLORS } from './menuIcons';

function MyComponent() {
  return (
    <FrameIcon 
      size={24} 
      color={ICON_COLORS.PRIMARY} 
    />
  );
}
```

### 3. 动态图标使用

```jsx
import { DynamicIcon } from './menuIcons';

function MyComponent({ iconName }) {
  return (
    <DynamicIcon 
      name={iconName} 
      size={24} 
      color="#1890ff" 
    />
  );
}
```

### 4. 在菜单中使用

```jsx
import { OpenSvgIcon } from './menuIcons';

<Menu.SubMenu
  title={(
    <span>
      <OpenSvgIcon size={16} color="#1890ff" style={{ marginRight: 8 }} />
      <span>菜单项</span>
    </span>
  )}
>
```

## API

### 通用Props

所有图标组件都支持以下props：

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| size | number \| string | 16 | 图标大小，数字时单位为px |
| color | string | 'currentColor' | 图标颜色，支持任何CSS颜色值 |
| className | string | - | 自定义CSS类名 |
| style | object | - | 自定义样式对象 |

### 预设颜色常量

```javascript
ICON_COLORS = {
  PRIMARY: '#1890ff',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#f5222d',
  INFO: '#1890ff',
  DEFAULT: 'currentColor',
  DISABLED: '#d9d9d9',
}
```

## 使用示例

### 不同颜色

```jsx
<FrameIcon color="#1890ff" />      // 蓝色
<FrameIcon color="#52c41a" />      // 绿色
<FrameIcon color="#faad14" />      // 黄色
<FrameIcon color="#f5222d" />      // 红色
```

### 不同尺寸

```jsx
<FrameIcon size={12} />   // 12px
<FrameIcon size={16} />   // 16px (默认)
<FrameIcon size={24} />   // 24px
<FrameIcon size="2em" />  // 2em
```

### 继承父元素颜色

```jsx
<div style={{ color: '#1890ff' }}>
  <FrameIcon />  {/* 自动使用蓝色 */}
</div>
```

## 迁移指南

### 从原有代码迁移

```jsx
// 旧的用法（有语法错误）
export OpenSvgIcon = props => <Icon component={Frame} {...props} />;

// 新的用法
export const OpenSvgIcon = (props) => <IconBase SvgComponent={Frame} {...props} />;
```

### 添加动态颜色支持

```jsx
// 原有用法
<OpenSvgIcon />

// 新用法 - 支持动态颜色
<OpenSvgIcon color="#1890ff" size={16} />
```

## 注意事项

1. 图标组件基于SVG实现，在现代浏览器中有良好支持
2. 建议使用预设的颜色常量保持设计一致性
3. 动态图标组件会在控制台警告未找到的图标名称
4. 保持了与原有 `OpenSvgIcon` 的向后兼容性
