# 菜单图标组件

统一导出SVG图标为Icon组件，专为LeftMenu组件设计。

## 特性

- 📦 **统一导出**：将所有SVG图标统一导出为Icon组件
- 🔧 **简单易用**：基于Antd Icon组件，使用方式一致
- 🎯 **向后兼容**：保持与原有代码的兼容性

## 可用图标

- `FrameIcon` - Frame.svg
- `Frame1Icon` - Frame(1).svg
- `Frame2Icon` - Frame(2).svg
- `Frame3Icon` - Frame(3).svg
- `Frame4Icon` - Frame(4).svg
- `Frame5Icon` - Frame(5).svg
- `Frame6Icon` - Frame(6).svg
- `Frame7Icon` - Frame(7).svg
- `Frame8Icon` - Frame(8).svg
- `Frame9Icon` - Frame(9).svg
- `Frame10Icon` - Frame(10).svg
- `Frame11Icon` - Frame(11).svg
- `Frame12Icon` - Frame(12).svg
- `Frame13Icon` - Frame(13).svg
- `Frame14Icon` - Frame(14).svg
- `Frame15Icon` - Frame(15).svg
- `Frame16Icon` - Frame(16).svg
- `Frame17Icon` - Frame(17).svg

## 基础用法

### 1. 直接使用图标组件

```jsx
import { FrameIcon, Frame1Icon } from './menuIcons';

function MyComponent() {
  return (
    <div>
      <FrameIcon />
      <Frame1Icon />
    </div>
  );
}
```

### 2. 在菜单中使用

```jsx
import { OpenSvgIcon } from './menuIcons';

<Menu.SubMenu
  title={(
    <span>
      <OpenSvgIcon style={{ marginRight: 8 }} />
      <span>菜单项</span>
    </span>
  )}
>
```

## API

所有图标组件都是基于Antd Icon组件，支持Icon组件的所有props。

## 迁移指南

### 修复语法错误

```jsx
// 旧的用法（有语法错误）
export OpenSvgIcon = props => <Icon component={Frame} {...props} />;

// 新的用法
export const OpenSvgIcon = props => <Icon component={Frame} {...props} />;
```

## 注意事项

1. 图标组件基于Antd Icon和SVG实现
2. 保持了与原有 `OpenSvgIcon` 的向后兼容性
3. 所有图标都统一导出为Icon组件
