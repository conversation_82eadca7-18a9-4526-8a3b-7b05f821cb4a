import { ReactComponent as Frame } from './assets/Frame.svg';
import { ReactComponent as Frame1 } from './assets/Frame(1).svg';
import { ReactComponent as Frame2 } from './assets/Frame(2).svg';
import { ReactComponent as Frame3 } from './assets/Frame(3).svg';
import { ReactComponent as Frame4 } from './assets/Frame(4).svg';
import { ReactComponent as Frame5 } from './assets/Frame(5).svg';
import { ReactComponent as Frame6 } from './assets/Frame(6).svg';
import { ReactComponent as Frame7 } from './assets/Frame(7).svg';
import { ReactComponent as Frame8 } from './assets/Frame(8).svg';
import { ReactComponent as Frame9 } from './assets/Frame(9).svg';
import { ReactComponent as Frame10 } from './assets/Frame(10).svg';
import { ReactComponent as Frame11 } from './assets/Frame(11).svg';
import { ReactComponent as Frame12 } from './assets/Frame(12).svg';
import { ReactComponent as Frame13 } from './assets/Frame(13).svg';
import { ReactComponent as Frame14 } from './assets/Frame(14).svg';
import { ReactComponent as Frame15 } from './assets/Frame(15).svg';
import { ReactComponent as Frame16 } from './assets/Frame(16).svg';
import { ReactComponent as Frame17 } from './assets/Frame(17).svg';

import { Icon } from 'antd';
export OpenSvgIcon = props => <Icon component={Frame} {...props} />;

