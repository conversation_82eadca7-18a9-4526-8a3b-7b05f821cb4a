import React from 'react';
import { Icon } from 'antd';
import { ReactComponent as Frame } from './assets/Frame.svg';
import { ReactComponent as Frame1 } from './assets/Frame(1).svg';
import { ReactComponent as Frame2 } from './assets/Frame(2).svg';
import { ReactComponent as Frame3 } from './assets/Frame(3).svg';
import { ReactComponent as Frame4 } from './assets/Frame(4).svg';
import { ReactComponent as Frame5 } from './assets/Frame(5).svg';
import { ReactComponent as Frame6 } from './assets/Frame(6).svg';
import { ReactComponent as Frame7 } from './assets/Frame(7).svg';
import { ReactComponent as Frame8 } from './assets/Frame(8).svg';
import { ReactComponent as Frame9 } from './assets/Frame(9).svg';
import { ReactComponent as Frame10 } from './assets/Frame(10).svg';
import { ReactComponent as Frame11 } from './assets/Frame(11).svg';
import { ReactComponent as Frame12 } from './assets/Frame(12).svg';
import { ReactComponent as Frame13 } from './assets/Frame(13).svg';
import { ReactComponent as Frame14 } from './assets/Frame(14).svg';
import { ReactComponent as Frame15 } from './assets/Frame(15).svg';
import { ReactComponent as Frame16 } from './assets/Frame(16).svg';
import { ReactComponent as Frame17 } from './assets/Frame(17).svg';

/**
 * 基础图标组件，支持动态颜色和大小
 * 使用Antd Icon组件包装，确保兼容性
 */
const IconBase = ({ SvgComponent, ...props }) => {
  return <Icon component={SvgComponent} {...props} />;
};

// 导出所有图标组件，支持动态颜色
export const FrameIcon = props => <IconBase SvgComponent={Frame} {...props} />;
export const Frame1Icon = props => <IconBase SvgComponent={Frame1} {...props} />;
export const Frame2Icon = props => <IconBase SvgComponent={Frame2} {...props} />;
export const Frame3Icon = props => <IconBase SvgComponent={Frame3} {...props} />;
export const Frame4Icon = props => <IconBase SvgComponent={Frame4} {...props} />;
export const Frame5Icon = props => <IconBase SvgComponent={Frame5} {...props} />;
export const Frame6Icon = props => <IconBase SvgComponent={Frame6} {...props} />;
export const Frame7Icon = props => <IconBase SvgComponent={Frame7} {...props} />;
export const Frame8Icon = props => <IconBase SvgComponent={Frame8} {...props} />;
export const Frame9Icon = props => <IconBase SvgComponent={Frame9} {...props} />;
export const Frame10Icon = props => <IconBase SvgComponent={Frame10} {...props} />;
export const Frame11Icon = props => <IconBase SvgComponent={Frame11} {...props} />;
export const Frame12Icon = props => <IconBase SvgComponent={Frame12} {...props} />;
export const Frame13Icon = props => <IconBase SvgComponent={Frame13} {...props} />;
export const Frame14Icon = props => <IconBase SvgComponent={Frame14} {...props} />;
export const Frame15Icon = props => <IconBase SvgComponent={Frame15} {...props} />;
export const Frame16Icon = props => <IconBase SvgComponent={Frame16} {...props} />;
export const Frame17Icon = props => <IconBase SvgComponent={Frame17} {...props} />;

// 为了向后兼容，保留原有的导出名称
export const OpenSvgIcon = FrameIcon;

// 图标映射对象，便于动态使用
export const IconMap = {
  frame: FrameIcon,
  frame1: Frame1Icon,
  frame2: Frame2Icon,
  frame3: Frame3Icon,
  frame4: Frame4Icon,
  frame5: Frame5Icon,
  frame6: Frame6Icon,
  frame7: Frame7Icon,
  frame8: Frame8Icon,
  frame9: Frame9Icon,
  frame10: Frame10Icon,
  frame11: Frame11Icon,
  frame12: Frame12Icon,
  frame13: Frame13Icon,
  frame14: Frame14Icon,
  frame15: Frame15Icon,
  frame16: Frame16Icon,
  frame17: Frame17Icon,
};

// 动态图标组件
export const DynamicIcon = ({ name, ...props }) => {
  const IconComponent = IconMap[name];
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`);
    return null;
  }
  return <IconComponent {...props} />;
};

// 预设颜色常量
export const ICON_COLORS = {
  PRIMARY: '#1890ff',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#f5222d',
  INFO: '#1890ff',
  DEFAULT: 'currentColor',
  DISABLED: '#d9d9d9',
};
